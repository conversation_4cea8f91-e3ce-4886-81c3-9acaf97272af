version: '3.8'

services:
  traefik:
    image: traefik:v2.10
    command:
      - "--configFile=/etc/traefik/traefik.yml"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/traefik.yml:/etc/traefik/traefik.yml:ro
      - /etc/letsencrypt/live/eko-api2.nextai.asia:/etc/letsencrypt/live/eko-api2.nextai.asia:ro
    networks:
      - web
    restart: unless-stopped

  app:
    image: eko-backend:latest
    networks:
      - web
    environment:
      - PORT=8000
    extra_hosts:
      - minio.nextai.asia:*************
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.app.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.app.entrypoints=websecure"
      - "traefik.http.routers.app.tls=true"
      - "traefik.http.services.app.loadbalancer.server.port=8000"
      - "traefik.http.routers.app-redirect.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.app-redirect.entrypoints=web"
      - "traefik.http.routers.app-redirect.middlewares=https-redirect"
      - "traefik.http.middlewares.https-redirect.redirectscheme.scheme=https"

networks:
  web:
    driver: bridge
